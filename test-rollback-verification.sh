#!/bin/bash

# Git 回滚验证测试脚本
# 用于验证回滚操作是否成功完成

echo "=== Git 回滚验证测试脚本 ==="
echo "开始时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "测试 $TOTAL_TESTS: $test_name ... "
    
    result=$(eval "$test_command" 2>/dev/null)
    
    if [[ "$result" == *"$expected_result"* ]]; then
        echo -e "${GREEN}PASS${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}FAIL${NC}"
        echo "  期望: $expected_result"
        echo "  实际: $result"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 检查是否在git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}错误: 当前目录不是git仓库${NC}"
    exit 1
fi

echo "=== 基础Git状态检查 ==="

# 测试1: 检查当前HEAD是否指向目标commit
run_test "检查HEAD指向目标commit" \
    "git rev-parse HEAD" \
    "7531d533bdf68b5bbd3246fd2a4003347defa492"

# 测试2: 检查当前分支是否为main
run_test "检查当前分支为main" \
    "git branch --show-current" \
    "main"

# 测试3: 检查工作目录是否干净
run_test "检查工作目录状态" \
    "git status --porcelain" \
    ""

# 测试4: 检查与远程分支同步状态
run_test "检查与远程分支同步" \
    "git status | grep 'up to date'" \
    "up to date"

echo ""
echo "=== Commit信息验证 ==="

# 测试5: 验证commit消息
run_test "验证commit消息" \
    "git log -1 --pretty=format:'%s'" \
    "Merge branch 'main' of https://github.com/LibreSpark/LibreTV"

# 测试6: 验证commit作者
run_test "验证commit作者" \
    "git log -1 --pretty=format:'%an'" \
    "GH Action - Upstream Sync"

# 测试7: 验证commit日期
run_test "验证commit日期" \
    "git log -1 --pretty=format:'%ad' --date=iso" \
    "2025-08-02"

echo ""
echo "=== 历史记录验证 ==="

# 测试8: 检查特定commit不在历史中
echo -n "测试 $((TOTAL_TESTS + 1)): 验证回滚的commit已被移除 ... "
TOTAL_TESTS=$((TOTAL_TESTS + 1))

if git log --oneline | grep -q "4d2d4f8"; then
    echo -e "${RED}FAIL${NC}"
    echo "  错误: 发现应该被回滚的commit 4d2d4f8"
    FAILED_TESTS=$((FAILED_TESTS + 1))
else
    echo -e "${GREEN}PASS${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# 测试9: 检查回滚前的最近5个commit
echo -n "测试 $((TOTAL_TESTS + 1)): 验证当前历史记录 ... "
TOTAL_TESTS=$((TOTAL_TESTS + 1))

current_commits=$(git log --oneline -5 | cut -d' ' -f1)
expected_commits="7531d53 f6b26de 5836cc6 aa357ce ae2a48d"

if [[ "$current_commits" == *"7531d53"* ]] && [[ "$current_commits" == *"f6b26de"* ]]; then
    echo -e "${GREEN}PASS${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}FAIL${NC}"
    echo "  当前历史记录与期望不符"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

echo ""
echo "=== 文件系统验证 ==="

# 测试10: 检查关键文件是否存在
key_files=("README.md" ".gitignore")
for file in "${key_files[@]}"; do
    echo -n "测试 $((TOTAL_TESTS + 1)): 检查文件 $file 存在 ... "
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ -f "$file" ]]; then
        echo -e "${GREEN}PASS${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${YELLOW}SKIP${NC} (文件不存在，可能正常)"
        # 不计入失败，因为某些文件可能在回滚后不存在
    fi
done

echo ""
echo "=== 远程仓库验证 ==="

# 测试11: 检查远程分支状态
run_test "检查远程分支同步状态" \
    "git fetch origin && git status | grep 'up to date'" \
    "up to date"

echo ""
echo "=== 测试结果汇总 ==="
echo "总测试数: $TOTAL_TESTS"
echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败: ${RED}$FAILED_TESTS${NC}"
echo -e "跳过: ${YELLOW}$((TOTAL_TESTS - PASSED_TESTS - FAILED_TESTS))${NC}"

if [[ $FAILED_TESTS -eq 0 ]]; then
    echo ""
    echo -e "${GREEN}✅ 所有关键测试通过！回滚操作验证成功。${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}❌ 发现 $FAILED_TESTS 个测试失败，请检查回滚状态。${NC}"
    exit 1
fi
